import time
import requests
import random
from ._email_server import EmailServer

class ShanMail(EmailServer):
    """闪邮箱API邮箱服务实现"""
    
    BASE_URL = "https://zizhu.shanyouxiang.com"

    def __init__(self, api_key, email_type="outlook", count=1):
        """
        初始化闪邮箱API客户端
        
        Args:
            api_key: API密钥
            email_type: 邮箱类型，支持"outlook"或"hotmail"
            count: 需要获取的邮箱数量
        """
        self.api_key = api_key
        self.email_type = email_type
        self.count = count
        self.email_address = None
        self.email_password = None
        
        # 获取一个邮箱
        self._get_email()
    
    def _get_email(self):
        """从闪邮箱API获取邮箱"""
        try:
            response = requests.get(
                f"{self.BASE_URL}/huoqu",
                params={
                    "card": self.api_key,
                    "shuliang": 1,
                    "leixing": self.email_type
                }
            )
            
            if response.status_code == 200:
                email_data = response.text.strip()
                if email_data:
                    # 解析返回的邮箱和密码
                    email_password = email_data.split("----")
                    if len(email_password) == 2:
                        self.email_address = email_password[0].strip()
                        self.email_password = email_password[1].strip()
                        print(f"[ShanMail] Successfully obtained email: {self.email_address}")
                        return True
            
            print(f"[ShanMail] Failed to get email. Status code: {response.status_code}, Response: {response.text}")
            return False
        
        except Exception as e:
            print(f"[ShanMail] Error when getting email: {str(e)}")
            return False
    
    def get_email_address(self):
        """获取邮箱地址"""
        return self.email_address
    
    def wait_for_new_message(self, delay=5, timeout=60):
        """
        等待新邮件 - 注意：闪邮箱API不支持获取邮件内容
        这个方法需要手动处理验证码
        """
        print("[ShanMail] Warning: ShanMail API does not support retrieving email content.")
        print(f"[ShanMail] Please manually handle verification code for: {self.email_address}")
        print(f"[ShanMail] Email password: {self.email_password}")
        
        # 由于无法自动获取邮件，这里模拟等待用户输入验证码
        verification_code = input("[ShanMail] Please enter the verification code manually: ")
        
        # 模拟邮件数据结构
        return {
            "from": "<EMAIL>",
            "to": self.email_address,
            "subject": "Your Cursor verification code",
            "content": f"Your verification code is {verification_code}. This code expires in 10 minutes.",
            "body_text": f"\n{verification_code}\n"
        }

    @classmethod
    def check_balance(cls, api_key):
        """查询账户余额"""
        try:
            response = requests.get(f"{cls.BASE_URL}/yue", params={"card": api_key})
            if response.status_code == 200:
                return response.json().get("num", 0)
            return 0
        except Exception as e:
            print(f"[ShanMail] Error when checking balance: {str(e)}")
            return 0
    
    @classmethod
    def check_inventory(cls):
        """查询邮箱库存"""
        try:
            response = requests.get(f"{cls.BASE_URL}/kucun")
            if response.status_code == 200:
                return response.json()
            return {"hotmail": 0, "outlook": 0}
        except Exception as e:
            print(f"[ShanMail] Error when checking inventory: {str(e)}")
            return {"hotmail": 0, "outlook": 0} 