# 项目记忆库 (Memory Bank)

## 项目概述

本项目包含两个主要功能模块：

1. **Cursor-Register**: 一个自动化的Cursor账号注册工具，支持批量注册Cursor账号并管理令牌
2. **视频旋转工具**: 批量视频文件旋转处理的Windows批处理脚本

### 核心功能
- **Cursor账号自动注册**: 支持临时邮箱和IMAP邮箱验证
- **令牌管理**: 自动保存账号信息和令牌到CSV文件
- **Chat-API集成**: 支持将令牌上传到Chat-API服务
- **低余额账号管理**: 自动清理和管理Chat-API中的低余额账号
- **Github Action支持**: 支持在Github Action环境中运行
- **视频批量处理**: 使用FFmpeg进行视频旋转，支持GPU加速

## 项目结构

```mermaid
graph TD
    A[项目根目录] --> B[Cursor-Register/]
    A --> C[视频处理脚本]
    A --> D[cursor_reset-windows.exe]
    
    B --> E[cursor_register.py - 主程序]
    B --> F[config/ - 配置文件]
    B --> G[helper/ - 辅助模块]
    B --> H[tokenManager/ - 令牌管理]
    B --> I[turnstilePatch/ - 浏览器补丁]
    B --> J[requirements.txt - 依赖]
    
    F --> K[config.yaml - 主配置]
    F --> L[config_shanmail.yaml - 邮箱配置]
    
    G --> M[cursor_register.py - 注册逻辑]
    G --> N[email/ - 邮箱处理]
    
    H --> O[cursor.py - Cursor API]
    H --> P[oneapi_cursor_cleaner.py - 清理工具]
    H --> Q[oneapi_manager.py - API管理]
    
    C --> R[rotate-videos-folder.bat - 批量旋转]
    C --> S[批量旋转视频_选择旋转方向.bat]
    C --> T[拖放文件夹到这里旋转视频.bat]
```

### 关键目录说明
- `Cursor-Register/`: 主要的Python项目，包含Cursor账号注册和管理功能
- `config/`: 配置文件目录，包含注册参数、邮箱设置、OneAPI配置等
- `helper/`: 辅助功能模块，包含注册逻辑和邮箱处理
- `tokenManager/`: 令牌管理模块，负责与OneAPI交互和账号清理

## 代码特点与规范

### 编程语言与框架
- **主要语言**: Python 3.10+
- **配置管理**: Hydra + OmegaConf (YAML配置)
- **浏览器自动化**: DrissionPage (基于Chromium)
- **并发处理**: concurrent.futures (多线程)
- **邮箱处理**: temp_mails, IMAP协议
- **数据生成**: Faker (虚假数据生成)

### 架构模式
- **模块化设计**: 功能按模块分离（注册、邮箱、令牌管理）
- **配置驱动**: 使用YAML配置文件管理所有参数
- **多线程支持**: 支持并发注册多个账号
- **插件化邮箱**: 支持多种邮箱服务（临时邮箱、IMAP、ShanMail）

### 编码规范
- **命名风格**: 下划线命名法 (snake_case)
- **配置结构**: 层次化YAML配置
- **错误处理**: try-catch异常处理机制
- **日志输出**: 详细的处理状态输出

### 关键设计决策
1. **浏览器选择**: 使用DrissionPage而非Selenium，提供更好的性能和稳定性
2. **配置管理**: 采用Hydra框架，支持命令行参数覆盖配置文件
3. **邮箱策略**: 支持多种邮箱服务，适应不同使用场景
4. **令牌存储**: 本地CSV文件存储，同时支持上传到OneAPI
5. **GPU加速**: 视频处理脚本支持NVIDIA GPU加速

## 重要依赖与配置

### Python依赖 (requirements.txt)
```
hydra-core          # 配置管理框架
DrissionPage        # 浏览器自动化
faker              # 虚假数据生成
temp_mails         # 临时邮箱服务
beautifulsoup4     # HTML解析
```

### 关键配置参数
- `register.number`: 注册账号数量
- `register.max_workers`: 并发线程数
- `register.email_server.name`: 邮箱服务类型
- `oneapi.enabled`: 是否启用OneAPI集成
- `oneapi.url`: OneAPI服务地址
- `oneapi.token`: OneAPI访问令牌

### 环境变量
- `HIDE_ACCOUNT_INFO`: 隐藏账号信息输出
- `ENABLE_HEADLESS`: 启用无头浏览器模式
- `ENABLE_BROWSER_LOG`: 启用浏览器日志

### 外部工具依赖
- **FFmpeg**: 视频处理工具，支持GPU加速编码
- **Chrome/Chromium**: 浏览器引擎，用于自动化操作

## 更新日志

### 2025-01-04 - 初始记忆库创建
- 分析整个项目结构，创建第一版记忆库
- 识别出Cursor注册工具和视频处理工具两个主要功能模块
- 记录了项目的技术栈、架构模式和关键配置信息
- 建立了项目结构的Mermaid图表表示
