name: Cursor Register

on:
  workflow_dispatch:
    inputs:
      number:
        default: "2"
        type: number
      max_workers:
        default: "1"
        type: number
      email_server:
        type: choice
        options:
        - TempEmail
        - IMAP
      ingest_to_oneapi:
        description: 'Ingest account tokens to OneAPI'
        type: boolean
      upload_artifact:
        description: 'Upload account infos to artifact'
        type: boolean
        default: true

jobs:
  build:
    runs-on: windows-latest
    env:
      HIDE_ACCOUNT_INFO: "true"
      ENABLE_BROWSER_LOG: "false"
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - name: Install requirements
        run: |
          pip3 install -r ./requirements.txt

      - name: "[TempEmail][OneAPI] Run script"
        if: ${{ github.event.inputs.email_server == 'TempEmail' && github.event.inputs.ingest_to_oneapi == 'true' }}
        run: |
          python3 ./cursor_register.py register.number=${{ github.event.inputs.number }} register.max_workers=${{ github.event.inputs.max_workers }} `
          register.email_server.name="temp_email_server" `
          oneapi.enabled=true oneapi.url="${{ secrets.CURSOR_ONEAPI_URL }}" oneapi.token="${{ secrets.CURSOR_ONEAPI_TOKEN }}" oneapi.channel_url="${{ secrets.CURSOR_CHANNEL_URL }}" 

      - name: "[TempEmail][CSV] Run script"
        if: ${{ github.event.inputs.email_server == 'TempEmail' && github.event.inputs.ingest_to_oneapi == 'false' && github.event.inputs.upload_artifact == 'true' }}
        run: |
          python3 ./cursor_register.py register.number=${{ github.event.inputs.number }} register.max_workers=${{ github.event.inputs.max_workers }} `
          register.email_server.name="temp_email_server"

      - name: "[IMAP][OneAPI] Run script"
        if: ${{ github.event.inputs.email_server == 'IMAP' && github.event.inputs.ingest_to_oneapi == 'true' }}
        run: |
          python3 ./cursor_register.py register.number=${{ github.event.inputs.number }} register.max_workers=${{ github.event.inputs.max_workers }} `
          register.email_server.name="imap_email_server" register.email_server.use_custom_address=true register.email_server.custom_email_address="[${{ secrets.CURSOR_CUSTOM_EMAIL_ADDRESS }}]" `
          register.imap_email_server.imap_server="${{ secrets.CURSOR_IMAP_SERVER }}" register.imap_email_server.imap_port="${{ secrets.CURSOR_IMAP_PORT }}" register.imap_email_server.username="${{ secrets.CURSOR_IMAP_USERNAME }}" register.imap_email_server.password="${{ secrets.CURSOR_IMAP_PASSWORD }}" `
          oneapi.enabled=true oneapi.url="${{ secrets.CURSOR_ONEAPI_URL }}" oneapi.token="${{ secrets.CURSOR_ONEAPI_TOKEN }}" oneapi.channel_url="${{ secrets.CURSOR_CHANNEL_URL }}"           

      - name: "[IMAP][CSV] Run script"
        if: ${{ github.event.inputs.email_server == 'IMAP' && github.event.inputs.ingest_to_oneapi == 'false' && github.event.inputs.upload_artifact == 'true' }}
        run: |
          python3 ./cursor_register.py register.number=${{ github.event.inputs.number }} register.max_workers=${{ github.event.inputs.max_workers }} `
          register.email_server.name="imap_email_server" register.email_server.use_custom_address=true register.email_server.custom_email_address="[${{ secrets.CURSOR_CUSTOM_EMAIL_ADDRESS }}]" `
          register.imap_email_server.imap_server="${{ secrets.CURSOR_IMAP_SERVER }}" register.imap_email_server.imap_port="${{ secrets.CURSOR_IMAP_PORT }}" register.imap_email_server.username="${{ secrets.CURSOR_IMAP_USERNAME }}" register.imap_email_server.password="${{ secrets.CURSOR_IMAP_PASSWORD }}" `

      - name: Upload csv files to artifact
        if: ${{ github.event.inputs.upload_artifact == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: "Account Info"
          path: "*.csv"
          retention-days: 1
