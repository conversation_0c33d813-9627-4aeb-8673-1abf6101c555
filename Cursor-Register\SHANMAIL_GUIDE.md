# 使用闪邮箱API注册Cursor指南

本指南将帮助您使用闪邮箱API进行Cursor账号批量注册。

## 背景

闪邮箱API提供了一种获取大量Outlook和Hotmail邮箱的方式，可用于批量注册Cursor账号。与临时邮箱服务不同，这些邮箱是真实的，可以长期使用，但有一个重要限制：**目前闪邮箱API只能提供邮箱账号和密码，无法自动获取邮箱中的验证码**。

这意味着注册过程中需要手动处理验证码部分，有两种方式：

1. 使用邮箱密码手动登录查看验证码
2. 注册时在控制台手动输入验证码

## 准备工作

1. 获取闪邮箱API密钥（需要付费购买）：https://shanyouxiang.com/shanMail.html
2. 确保您的账户余额足够注册所需的账号数量
3. 查看库存确保有足够的邮箱可用

## 配置说明

1. 复制 `config/config_shanmail.yaml` 文件为 `config/config.yaml`（或直接使用）
2. 编辑配置文件中的以下内容：

```yaml
register:
  number: 10                                 # 需要注册的账号数量
  max_workers: 2                             # 多线程工作器数量（建议不要太高）
  email_server:
    name: shanmail_server                    # 使用闪邮箱服务
    email_type: outlook                      # 邮箱类型：outlook 或 hotmail

  shanmail_server:
    api_key: YOUR_API_KEY_HERE               # 闪邮箱API密钥
    email_type: outlook                      # 邮箱类型：outlook 或 hotmail
```

## 使用方法

### 本地运行

```bash
python cursor_register.py
```

### 使用指定配置文件

```bash
python cursor_register.py --config-name config_shanmail
```

## 特别说明

### 验证码处理

当程序运行到需要验证码部分时，会提示您手动输入验证码：

```
[ShanMail] Warning: ShanMail API does not support retrieving email content.
[ShanMail] Please manually handle verification code for: <EMAIL>
[ShanMail] Email password: password123
[ShanMail] Please enter the verification code manually:
```

此时，您需要：
1. 使用提供的邮箱和密码登录对应的邮箱服务（Outlook/Hotmail）
2. 查找来自Cursor的验证码邮件
3. 将验证码输入到程序中

### 账号保存

成功注册的账号信息将保存在以下文件中：
- `output_YYYY-MM-DD.csv`：包含邮箱和令牌信息
- `token_YYYY-MM-DD.csv`：仅包含令牌信息

## 与OneAPI集成

如果需要将注册的账号自动上传到OneAPI，请按照以下步骤配置：

```yaml
oneapi:
  enabled: true                              # 启用OneAPI集成
  url: http://your-oneapi-server:3000        # OneAPI服务器地址
  token: your_oneapi_token                   # OneAPI访问令牌
  channel_url: http://your-cursor-api:3010   # Cursor API地址
```

## 验证码超时问题

如果在输入验证码过程中遇到超时问题，可以在 `helper/email/shanmail.py` 文件中修改 `wait_for_new_message` 方法的 `timeout` 参数（默认为60秒）。

## 故障排除

1. **账户余额不足**：确保您的闪邮箱账户有足够的余额
2. **库存不足**：检查闪邮箱的库存是否满足注册需求
3. **验证码超时**：尝试增加超时时间或减少并行线程数
4. **注册失败率高**：可能是由于网络问题，尝试使用代理或减少同时注册的账号数量
5. **邮箱登录失败**：确认邮箱密码正确，可能需要重新获取邮箱

## 注意事项

1. 闪邮箱提供的邮箱是真实的，请遵守服务条款和法律法规
2. 请谨慎保管您的API密钥和获取的邮箱信息
3. 批量注册可能会触发服务商的限制措施，请合理使用 