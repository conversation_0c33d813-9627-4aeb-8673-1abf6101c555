@echo off
setlocal enabledelayedexpansion

title 批量视频旋转处理工具

:MENU
cls
echo ======================================================
echo            批量视频旋转处理工具（GPU加速）
echo ======================================================
echo.
echo  请选择旋转方向:
echo    1. 顺时针旋转90度（向右旋转）
echo    2. 逆时针旋转90度（向左旋转）
echo    3. 旋转180度（上下颠倒）
echo    4. 水平翻转（左右镜像）
echo    5. 垂直翻转（上下镜像）
echo    0. 退出
echo.
set /p CHOICE=请输入选项 (0-5): 

if "%CHOICE%"=="1" set "FILTER=transpose=1" & set "DIRECTION=顺时针90度" & goto FOLDER
if "%CHOICE%"=="2" set "FILTER=transpose=2" & set "DIRECTION=逆时针90度" & goto FOLDER
if "%CHOICE%"=="3" set "FILTER=transpose=1,transpose=1" & set "DIRECTION=180度" & goto FOLDER
if "%CHOICE%"=="4" set "FILTER=hflip" & set "DIRECTION=水平翻转" & goto FOLDER
if "%CHOICE%"=="5" set "FILTER=vflip" & set "DIRECTION=垂直翻转" & goto FOLDER
if "%CHOICE%"=="0" goto :EOF
goto MENU

:FOLDER
cls
echo ======================================================
echo            批量视频旋转处理工具（GPU加速）
echo ======================================================
echo 已选择旋转方向: %DIRECTION%
echo.
echo 请选择处理方式:
echo    1. 处理当前文件夹中的所有视频
echo    2. 指定一个文件夹进行处理
echo    0. 返回主菜单
echo.
set /p FOLDER_CHOICE=请输入选项 (0-2): 

if "%FOLDER_CHOICE%"=="1" set "TARGET_FOLDER=%CD%" & goto PROCESS
if "%FOLDER_CHOICE%"=="2" goto SPECIFY_FOLDER
if "%FOLDER_CHOICE%"=="0" goto MENU
goto FOLDER

:SPECIFY_FOLDER
cls
set "TARGET_FOLDER="
set /p TARGET_FOLDER=请输入文件夹完整路径 (或直接拖放文件夹到此窗口): 

REM 移除引号（如果用户拖放文件夹会自动添加引号）
set TARGET_FOLDER=%TARGET_FOLDER:"=%

if not exist "%TARGET_FOLDER%\" (
    echo.
    echo 错误："%TARGET_FOLDER%" 不是一个有效的文件夹。
    echo 按任意键重试...
    pause >nul
    goto SPECIFY_FOLDER
)

:PROCESS
cls
echo ======================================================
echo            批量视频旋转处理工具（GPU加速）
echo ======================================================
echo 旋转方向: %DIRECTION%
echo 处理文件夹: "%TARGET_FOLDER%"
echo.

REM 设置视频文件扩展名
set "VIDEO_EXTENSIONS=mp4 mkv avi mov wmv flv"
echo 支持的视频格式: %VIDEO_EXTENSIONS%
echo.
echo 正在搜索视频文件...
echo.

set "FILE_COUNT=0"
set "SUCCESS_COUNT=0"
set "FAIL_COUNT=0"

REM 为每个支持的扩展名搜索文件并处理
for %%E in (%VIDEO_EXTENSIONS%) do (
    for %%F in ("%TARGET_FOLDER%\*.%%E") do (
        set /a FILE_COUNT+=1
        
        set "INPUT_FILE=%%F"
        set "FILENAME=%%~nF"
        set "EXTENSION=%%~xF"
        set "OUTPUT_FILE=%%~dpF!FILENAME!_rotated!EXTENSION!"
        
        echo [!FILE_COUNT!] 正在处理: "%%~nxF"
        echo     输出为: "!FILENAME!_rotated!EXTENSION!"
        
        REM 使用ffmpeg进行视频旋转，并使用NVIDIA GPU加速
        ffmpeg -i "!INPUT_FILE!" -vf "!FILTER!" -c:v h264_nvenc -c:a copy "!OUTPUT_FILE!" -y -hide_banner -loglevel warning
        
        if !errorlevel! equ 0 (
            set /a SUCCESS_COUNT+=1
            echo     √ 成功
        ) else (
            set /a FAIL_COUNT+=1
            echo     × 失败
        )
        echo.
    )
)

echo 处理完成！
echo 总计处理: !FILE_COUNT! 个文件
echo 成功: !SUCCESS_COUNT! 个
echo 失败: !FAIL_COUNT! 个
echo.

if !FILE_COUNT! equ 0 (
    echo 没有找到视频文件。
    echo 请确保指定的文件夹中包含视频文件，或检查文件扩展名是否受支持。
    echo 支持的格式: %VIDEO_EXTENSIONS%
)

echo.
echo 如果您使用的不是NVIDIA显卡，请编辑此脚本，将h264_nvenc改为:
echo - AMD显卡: h264_amf
echo - Intel显卡: h264_qsv
echo - 或删除"-c:v h264_nvenc"以使用CPU编码（较慢）
echo.
echo 请选择:
echo    1. 返回主菜单
echo    0. 退出程序
echo.
set /p END_CHOICE=请输入选项 (0-1): 
if "%END_CHOICE%"=="1" goto MENU
goto :EOF 