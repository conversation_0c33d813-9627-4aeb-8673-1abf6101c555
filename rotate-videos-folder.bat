@echo off
setlocal enabledelayedexpansion

REM 设置输入文件夹（默认为当前目录）
set "INPUT_FOLDER=%~1"
if "%INPUT_FOLDER%"=="" set "INPUT_FOLDER=."

REM 设置视频文件扩展名
set "VIDEO_EXTENSIONS=mp4 mkv avi mov wmv flv"

echo 将处理文件夹: "%INPUT_FOLDER%"
echo 支持的视频格式: %VIDEO_EXTENSIONS%
echo.
echo 正在搜索视频文件...
echo.

set "FILE_COUNT=0"
set "SUCCESS_COUNT=0"
set "FAIL_COUNT=0"

REM 为每个支持的扩展名搜索文件并处理
for %%E in (%VIDEO_EXTENSIONS%) do (
    for %%F in ("%INPUT_FOLDER%\*.%%E") do (
        set /a FILE_COUNT+=1
        
        set "INPUT_FILE=%%F"
        set "FILENAME=%%~nF"
        set "EXTENSION=%%~xF"
        set "OUTPUT_FILE=%%~dpF!FILENAME!_rotated!EXTENSION!"
        
        echo [!FILE_COUNT!] 正在处理: "%%~nxF"
        echo     输出为: "!FILENAME!_rotated!EXTENSION!"
        
        REM 使用ffmpeg进行视频旋转，并使用NVIDIA GPU加速
        ffmpeg -i "!INPUT_FILE!" -vf "transpose=1" -c:v h264_nvenc -c:a copy "!OUTPUT_FILE!" -y -hide_banner -loglevel warning
        
        if !errorlevel! equ 0 (
            set /a SUCCESS_COUNT+=1
            echo     √ 成功
        ) else (
            set /a FAIL_COUNT+=1
            echo     × 失败
        )
        echo.
    )
)

echo 处理完成！
echo 总计处理: !FILE_COUNT! 个文件
echo 成功: !SUCCESS_COUNT! 个
echo 失败: !FAIL_COUNT! 个
echo.

if !FILE_COUNT! equ 0 (
    echo 没有找到视频文件。
    echo 请确保指定的文件夹中包含视频文件，或检查文件扩展名是否受支持。
    echo 支持的格式: %VIDEO_EXTENSIONS%
)

echo.
echo 如果您使用的不是NVIDIA显卡，请编辑此脚本，将h264_nvenc改为:
echo - AMD显卡: h264_amf
echo - Intel显卡: h264_qsv
echo - 或删除"-c:v h264_nvenc"以使用CPU编码（较慢）
echo.
echo 按任意键退出...
pause >nul
endlocal 